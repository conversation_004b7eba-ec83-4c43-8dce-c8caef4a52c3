import type { SVGProps } from "react";
const SvgSubscribe = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 17 16"
        {...props}
    >
        <path
            fill="currentColor"
            d="m4.094 12.914 3.184-1.592.106-.049a1.99 1.99 0 0 1 1.566 0l.106.049 3.184 1.592V2.569H4.093zm9.406.863-.002.05a.728.728 0 0 1-1.005.621l-.045-.02-3.956-1.978a.73.73 0 0 0-.65 0l-3.956 1.977-.046.021a.728.728 0 0 1-1.005-.621l-.002-.05V2.036c0-.39.306-.707.69-.726l.038-.001h9.249c.385.02.69.338.69.727z"
        />
    </svg>
);
export default SvgSubscribe;
