import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {colors} from '@/constants/colors';

export const containerCss = css`
    padding: 16px 20px 12px;
    position: relative;
    transition: all 0.3s ease;
    &:hover {
        position: relative;
        z-index: 1;
        background: ${colors.white};
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        padding-bottom: 48px;
        margin-bottom: -48px;
        width: auto;
        .hover-actions {
            opacity: 1;
            min-height: 32px;
            padding: 0 20px 16px;
        }
    }
`;

export const hoverActionsStyle = css`
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 0;
    opacity: 0;
    transition: all 0.3s ease;
`;

export const DescriptionContainer = styled.div`
    margin: 15px 0 13px;
    padding: 10px 12px 9px;
    background-color: ${colors['gray-3']};
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    height: 57px;
    overflow: hidden;
`;

export const DescriptionText = styled.div`
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
    overflow: hidden;
`;

export const EllipsisOverlay = styled.div`
    position: absolute;
    bottom: 9px;
    right: 12px;
    padding-left: 10px;
    background: linear-gradient(to right, transparent, ${colors['gray-3']} 50%);
    pointer-events: none;
`;

export const cardContentStyle = {
    overflow: 'hidden',
    flex: 1,
};

export const protocolTextStyle = {
    color: colors['gray-7'],
    fontSize: 12,
    lineHeight: '20px',
};

export const departmentTextStyle = {
    color: colors['gray-7'],
    fontSize: 12,
    marginBottom: 12,
};

export const dividerStyle = {
    margin: '16px 0 8px',
};

export const statsContainerStyle = css`
    cursor: pointer;
    color: ${colors['gray-7']};
    font-size: 12px;
    line-height: 18px;
    transition: color 0.2s ease;

    &:hover {
        color: ${colors.primary};
    }
`;

export const iconStyle = {
    width: 14,
    height: 14,
};

export const formatCount = (count: number): string => {
    if (count >= 10000) {
        return `${Math.floor(count / 10000)}w+`;
    }
    if (count >= 1000) {
        return `${Math.floor(count / 1000)}k+`;
    }
    return count.toString();
};

export const actionButtonStyle = {
    flex: 1,
    backgroundColor: '#F2F2F2',
    borderRadius: 4,
    border: 'none',
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
};
